from fastapi import APIRouter
from pydantic import BaseModel
import joblib
import numpy as np
import os

router = APIRouter()

# Load model
model_path = os.path.join("models", "price_predictor.pkl")
model = joblib.load(model_path)

# Define input schema
class PriceInput(BaseModel):
    selling_price: float
    competitor_price: float

@router.post("/predict-price")
def predict_price(data: PriceInput):
    price_diff = data.competitor_price - data.selling_price
    features = np.array([[data.selling_price, data.competitor_price, price_diff]])
    predicted_price = model.predict(features)[0]
    return {
        "recommended_price": round(predicted_price, 2),
        "input": data.dict()
    }
