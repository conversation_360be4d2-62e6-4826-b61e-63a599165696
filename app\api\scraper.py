# app/api/scraper.py

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from .scraper_proxy import scrape_price_with_proxy
from .scraper_utils import scrape_price_from_url  # Selenium fallback

router = APIRouter()

class ScrapeRequest(BaseModel):
    url: str

@router.post("/scrape-price")
def scrape_price(data: ScrapeRequest):
    # Try ScraperAPI first
    price = scrape_price_with_proxy(data.url)
    if price is not None:
        return {"competitor_price": price}

    # Fallback to Selenium if proxy fails
    print("🔁 Falling back to Selenium...")
    price = scrape_price_from_url(data.url)
    if price is not None:
        return {"competitor_price": price}

    raise HTTPException(status_code=500, detail="Failed to scrape price using both methods.")
