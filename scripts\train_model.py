import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error
import joblib
import os

# Load your cleaned dataset
df = pd.read_csv("data/cleaned_products.csv")
print(" Columns in dataset:", df.columns.tolist())

# Use the correct column name for price
if "Selling Price" not in df.columns:
    raise ValueError(" 'Selling Price' column not found in dataset.")

# Simulate competitor price (10% higher for now)
df["competitor_price"] = df["Selling Price"] * 1.1

# Feature engineering
df["price_diff"] = df["competitor_price"] - df["Selling Price"]

# Select features and target
features = ["Selling Price", "competitor_price", "price_diff"]
target = "Selling Price"

X = df[features]
y = df[target]

# Train/test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train model
model = RandomForestRegressor(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

# Evaluate
preds = model.predict(X_test)
mae = mean_absolute_error(y_test, preds)
print(f" MAE: {mae:.2f}")

# Ensure models directory exists
os.makedirs("models", exist_ok=True)

# Save model
model_path = "models/price_predictor.pkl"
joblib.dump(model, model_path)
print(f" Model saved to {model_path}")
