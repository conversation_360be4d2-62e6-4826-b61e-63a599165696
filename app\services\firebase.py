from firebase_admin import firestore
from app.core.firebase import *  # ensures Firebase is initialized

# Initialize Firestore client once
db = firestore.client()

def store_competitor_price(product_name: str, price: float, source: str = "Jumia"):
    try:
        print(" Storing competitor price...")
        doc_ref = db.collection("competitor_prices").document(product_name)
        doc_ref.set({
            "price": price,
            "source": source,
            "timestamp": firestore.SERVER_TIMESTAMP
        })
        print(" Competitor price stored.")
    except Exception as e:
        print(" Error storing competitor price:", e)

def log_prediction(product_id: str, selling_price: float, competitor_price: float, predicted_price: float):
    try:
        print("📡 Logging prediction to Firebase...")
        doc_ref = db.collection("price_predictions").document()
        doc_ref.set({
            "product_id": product_id,
            "selling_price": selling_price,
            "competitor_price": competitor_price,
            "predicted_price": predicted_price,
            "timestamp": firestore.SERVER_TIMESTAMP
        })
        print(" Prediction logged.")
    except Exception as e:
        print(" Error logging prediction:", e)

def store_payment_log(reference: str, email: str, amount: float):
    try:
        # Fetch metadata
        meta_doc = db.collection("payment_metadata").document(reference).get()
        metadata = meta_doc.to_dict() if meta_doc.exists else {}

        doc_ref = db.collection("payments").document(reference)
        doc_ref.set({
            "email": email,
            "amount": amount,
            "reference": reference,
            "product_id": metadata.get("product_id"),
            "predicted_price": metadata.get("predicted_price"),
            "timestamp": firestore.SERVER_TIMESTAMP
        })
        print("✅ Payment logged with metadata.")
    except Exception as e:
        print("❌ Error logging payment:", e)



def store_payment_metadata(reference: str, product_id: str, predicted_price: float):
    try:
        doc_ref = db.collection("payment_metadata").document(reference)
        doc_ref.set({
            "product_id": product_id,
            "predicted_price": predicted_price,
            "timestamp": firestore.SERVER_TIMESTAMP
        })
        print("📝 Stored payment metadata.")
    except Exception as e:
        print("❌ Error storing metadata:", e)


def store_failed_payment(reference: str, email: str, reason: str):
    try:
        doc_ref = db.collection("failed_payments").document(reference)
        doc_ref.set({
            "email": email,
            "reference": reference,
            "reason": reason,
            "timestamp": firestore.SERVER_TIMESTAMP
        })
        print(f"❌ Logged failed payment: {reference} — {reason}")
    except Exception as e:
        print("❌ Error logging failed payment:", e)
