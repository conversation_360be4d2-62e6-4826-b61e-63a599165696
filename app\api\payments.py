from fastapi import APIRouter, Request
from pydantic import BaseModel
import hmac
import hashlib

from app.core.config import PAYSTACK_SECRET_KEY
from app.services.paystack import initialize_transaction
from app.services.firebase import store_payment_log, store_payment_metadata
from app.services.firebase import store_payment_log, store_payment_metadata, store_failed_payment


router = APIRouter()

# Request model for initiating a payment
class PaymentRequest(BaseModel):
    email: str
    amount: float
    callback_url: str
    product_id: str
    predicted_price: float

@router.post("/checkout")
def checkout(data: PaymentRequest):
    try:
        # Initialize Paystack transaction
        transaction = initialize_transaction(
            email=data.email,
            amount=data.amount,
            callback_url=data.callback_url
        )

        # Store metadata for later use in webhook
        store_payment_metadata(
            reference=transaction["reference"],
            product_id=data.product_id,
            predicted_price=data.predicted_price
        )

        return {
            "authorization_url": transaction["authorization_url"],
            "reference": transaction["reference"],
            "product_id": data.product_id,
            "predicted_price": data.predicted_price
        }
    except Exception as e:
        return {"error": str(e)}
    
@router.post("/webhook")
async def paystack_webhook(request: Request):
    payload = await request.body()
    signature = request.headers.get("x-paystack-signature")

    expected_signature = hmac.new(
        PAYSTACK_SECRET_KEY.encode(),
        msg=payload,
        digestmod=hashlib.sha512
    ).hexdigest()

    if signature != expected_signature:
        return {"status": "unauthorized"}

    event = await request.json()
    event_type = event.get("event")
    data = event.get("data", {})
    reference = data.get("reference")

    if event_type == "charge.success":
        amount = data["amount"] / 100
        email = data["customer"]["email"]
        store_payment_log(reference, email, amount)

    elif event_type in ["charge.failed", "abandoned"]:
        reason = data.get("gateway_response", "Unknown reason")
        email = data.get("customer", {}).get("email", "unknown")
        store_failed_payment(reference, email, reason)

    return {"status": "received"}
