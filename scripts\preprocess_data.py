import pandas as pd
import re

# Load raw dataset
df = pd.read_csv("data/amazon_products.csv")

# Drop columns with mostly missing data
columns_to_drop = [
    "Brand Name", "Asin", "Upc Ean Code", "List Price", "Quantity", "Sku", "Stock",
    "Product Details", "Dimensions", "Color", "Ingredients", "Direction To Use",
    "Size Quantity Variant", "Product Description"
]
df.drop(columns=columns_to_drop, inplace=True)

# Drop rows with missing essential fields
df.dropna(subset=["Product Name", "Selling Price"], inplace=True)

# Clean and normalize price values
def extract_price(value):
    if isinstance(value, str):
        # Remove currency symbols and commas
        value = re.sub(r'[^\d\.\-]', '', value)
        # If it's a range like "74.99-249.99", take the lower bound
        if '-' in value:
            return float(value.split('-')[0])
        try:
            return float(value)
        except ValueError:
            return None
    return value

df["Selling Price"] = df["Selling Price"].apply(extract_price)

# Drop rows where price couldn't be parsed
df.dropna(subset=["Selling Price"], inplace=True)

# Fill missing values in other useful columns
df["Category"] = df["Category"].fillna("Unknown")
df["Model Number"] = df["Model Number"].fillna("N/A")

# Save cleaned dataset
df.to_csv("data/cleaned_products.csv", index=False)
print("✅ Cleaned dataset saved to data/cleaned_products.csv")
