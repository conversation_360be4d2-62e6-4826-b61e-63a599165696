import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def scrape_price_from_url(url):
    options = uc.ChromeOptions()
    # options.add_argument("--headless")  # Optional for debugging

    driver = uc.Chrome(options=options)

    try:
        driver.get(url)

        wait = WebDriverWait(driver, 10)

        # Use the exact selector you copied
        price_element = wait.until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div.prc"))
        )

        price = price_element.text.strip()
    except Exception as e:
        print("⚠️ Price tag not found:", e)
        price = None
    finally:
        driver.quit()

    return price
