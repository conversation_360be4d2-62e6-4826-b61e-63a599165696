# app/api/scraper_proxy.py

import os
from dotenv import load_dotenv
import requests
from bs4 import BeautifulSoup

# ✅ Load environment variables from .env file
load_dotenv()

# 🔐 Get the API key from the environment
SCRAPER_API_KEY = os.getenv("SCRAPER_API_KEY")

def scrape_price_with_proxy(url: str):
    try:
        if not SCRAPER_API_KEY:
            print("❌ SCRAPER_API_KEY not found in environment.")
            return None

        proxy_url = f"https://api.scraperapi.com?api_key={SCRAPER_API_KEY}&url={url}"
        response = requests.get(proxy_url, timeout=15)

        if response.status_code != 200:
            print("❌ ScraperAPI failed with status:", response.status_code)
            return None

        # ✅ Save HTML for inspection
        with open("proxy_response.html", "w", encoding="utf-8") as f:
            f.write(response.text)

        soup = BeautifulSoup(response.text, "html.parser")
        price_tag = soup.select_one("span.-prc")

        if not price_tag:
            print("⚠️ Price tag not found in proxy HTML.")
            return None

        price_text = price_tag.text.strip().replace("₵", "").replace(",", "")
        return float(price_text)

    except Exception as e:
        print("❌ Proxy scraping error:", e)
        return None
