from fastapi import APIRouter
from pydantic import BaseModel
import joblib
import numpy as np
import os
from app.services.firebase import log_prediction, db  # Add db here


from app.services.firebase import log_prediction  # 🔹 Import the Firebase logger

router = APIRouter()

# Load model
model_path = os.path.join("models", "price_predictor.pkl")
model = joblib.load(model_path)

# Input schema
class PriceInput(BaseModel):
    product_id: str
    selling_price: float
    competitor_price: float
@router.post("/predict")
def predict_price(data: PriceInput):
    product_id = data.product_id

    # Check for override
    override_doc = db.collection("price_overrides").document(product_id).get()
    if override_doc.exists:
        override_price = override_doc.to_dict()["override_price"]
        return {
            "recommended_price": override_price,
            "source": "override",
            "input": data.dict()
        }

    # Feature engineering
    price_diff = data.competitor_price - data.selling_price
    features = np.array([[data.selling_price, data.competitor_price, price_diff]])

    # Predict
    predicted_price = model.predict(features)[0]
    rounded_price = round(predicted_price, 2)

    # Log to Firebase
    log_prediction(
        product_id=data.product_id,
        selling_price=data.selling_price,
        competitor_price=data.competitor_price,
        predicted_price=rounded_price
    )

    return {
        "recommended_price": rounded_price,
        "source": "model",
        "input": data.dict()
    }
