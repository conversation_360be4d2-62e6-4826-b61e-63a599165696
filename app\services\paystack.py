import requests
from app.core.config import PAYSTACK_SECRET_KEY

PAYSTACK_BASE_URL = "https://api.paystack.co"

def initialize_transaction(email: str, amount: float, callback_url: str):
    url = f"{PAYSTACK_BASE_URL}/transaction/initialize"
    headers = {
        "Authorization": f"Bearer {PAYSTACK_SECRET_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "email": email,
        "amount": int(amount * 100),  # Paystack expects amount in kobo/pesewas
        "callback_url": callback_url
    }

    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    return response.json()["data"]
