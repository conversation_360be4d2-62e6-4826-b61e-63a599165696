import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def scrape_price_from_url(url):
    options = uc.ChromeOptions()
    options.add_argument("--headless")  # ✅ Enable headless mode
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--window-size=1920x1080")

    driver = uc.Chrome(version_main=138, options=options)

    try:
        print("🔍 Navigating to:", url)
        driver.get(url)
        wait = WebDriverWait(driver, 10)

        print("⏳ Waiting for price element...")
        price_element = wait.until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "span.-prc"))
        )

        # ✅ Save screenshot for debugging
        driver.save_screenshot("debug_screenshot.png")

        price_text = price_element.text.strip()
        print("✅ Raw price text:", price_text)

        if not price_text:
            print("⚠️ Price element found but text is empty.")
            return None

        cleaned_price = price_text.replace("₵", "").replace(",", "")
        print("💰 Cleaned price:", cleaned_price)

        return float(cleaned_price)

    except Exception as e:
        print("❌ Scraping failed:", e)
        driver.save_screenshot("error_screenshot.png")  # Save on failure too
        return None
    finally:
        driver.quit()
