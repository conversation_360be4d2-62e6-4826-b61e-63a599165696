import os
from dotenv import load_dotenv

# Explicitly load .env from the project root
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
dotenv_path = os.path.join(BASE_DIR, ".env")
load_dotenv(dotenv_path)

FIREBASE_CREDENTIALS_PATH = os.getenv("FIREBASE_CREDENTIALS_PATH")
PAYSTACK_SECRET_KEY = os.getenv("PAYSTACK_SECRET_KEY")

# Debug print to confirm
print(" FIREBASE_CREDENTIALS_PATH =", FIREBASE_CREDENTIALS_PATH)
