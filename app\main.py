from fastapi import FastAPI
from app.api import pricing, auth, payments
from app.api import pricing
from app.api import scraper


app = FastAPI(title="AI Dynamic Pricing Engine")

# Include routers
app.include_router(pricing.router, prefix="/pricing")
app.include_router(auth.router, prefix="/auth")
app.include_router(payments.router, prefix="/payments")
app.include_router(scraper.router, prefix="/scraper")

@app.get("/")
def root():
    return {"message": "Welcome to the AI Dynamic Pricing Engine"}


